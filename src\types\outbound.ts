/**
 * 出库功能相关的类型定义
 */

// KD码信息接口
export interface KdCodeInfo {
  /** 客户零件号 - 对应解析码中的key 10 */
  partCode: string
  /** 单包数量 - 对应解析码中的key 17 */
  singleQuantity: string
  /** 供货批次号 - 对应解析码中的key 18 */
  lotNo: string
  /** KD码 */
  kdCode?: string
}

// 随箱卡信息接口
export interface BoxCardInfo {
  /** 客户零件号 - 对应解析码中的key 10 */
  partCode: string
  /** 单包数量 - 对应解析码中的key 17 */
  singleQuantity: string
  /** 供货批次号 - 对应解析码中的key 18 */
  lotNo: string
  /** 随箱卡码 */
  boxCardCode?: string
}

// 出库校验请求接口
export interface OutboundVerifyRequest {
  /** KD件码 */
  kdCode: string
  /** 随箱卡码 */
  boxCardCode: string
}

// 出库校验响应接口
export interface OutboundVerifyResponse {
  /** 是否匹配成功 */
  isMatch: boolean
  /** KD码信息 */
  kdCodeInfo: KdCodeInfo
  /** 随箱卡信息 */
  boxCardInfo: BoxCardInfo
  /** 匹配结果消息 */
  message: string
}

// 出库确认请求接口
export interface OutboundConfirmRequest {
  /** KD件码 */
  kdCode: string
  /** 随箱卡码 */
  boxCardCode: string
  /** 操作员 */
  operator?: string
}

// 出库确认响应接口
export interface OutboundConfirmResponse {
  /** 出库单号 */
  outboundNumber: string
  /** 出库时间 */
  outboundTime: string
  /** 操作员 */
  operator: string
  /** 是否成功 */
  success: boolean
}

// API响应基础接口
export interface ApiResponse<T = any> {
  /** 响应码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
}
