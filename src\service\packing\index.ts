import { http } from '@/utils/http'

export interface IWorkOrderInfo {
  /** 任务ID */
  id: number
  /** 工单号 */
  workNo: string
  /** 状态 */
  state: string
  /** 包装数量 */
  amount: number
  /** 包装类型 */
  packageCategory: string
  /** 产品ID */
  productId: number
  /** 产品名称 */
  productName: string
  /** 零件编码 */
  partCode: string
  /** 零件名称 */
  partName: string
  /** 供应商名称 */
  supplierName: string
  /** 供应商编码 */
  supplierCode: string
  /** 单箱数量 */
  singleQuantity: number
  /** 批次号 */
  lotNo: string
  /** 码放层数 */
  levelNum: number
}

export function getWorkOrderListApi() {
  return http.get<IWorkOrderInfo[]>('/api/v1/packaging/workOrderList')
}

// 装箱的请求参数
export interface IRequestPackingInfo {
  workOrderId: number
  productCodes: string[]
}

// 装箱的响应参数
export interface IResponsePackingInfo {
  supplierName: string
  supplierCode: string
  partCode: string
  partName: string
  singleQuantity: number
  lotNo: string
  levelNum: number
  produceDate: string
  serialNo: string
  kdCode: string
  qrcode: string
}

export function submitPackingApi(data: IRequestPackingInfo) {
  return http.post<IResponsePackingInfo>('/api/v1/packaging/singlePacking', data)
}
