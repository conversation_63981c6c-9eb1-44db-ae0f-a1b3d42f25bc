/**
 * 代码解析工具类
 * 用于解析特定格式的代码字符串，将其转换为键值对映射
 */

/**
 * 解析代码字符串为 Map 对象
 *
 * 解析规则：
 * - # 前面的数字作为 key
 * - # 和 $ 符号中间的内容作为 value
 * - 最后一个片段可能没有 $ 结尾
 *
 * 示例输入：10#403002875AAAG$11#8BC$17#30$18#18891-CT1520210202$19#4$20#20210916$31#B1$
 * 解析结果：
 * - key: 10, value: 403002875AAAG
 * - key: 11, value: 8BC
 * - key: 17, value: 30
 * - key: 18, value: 18891-CT1520210202
 * - key: 19, value: 4
 * - key: 20, value: 20210916
 * - key: 31, value: B1
 *
 * @param codeString 待解析的代码字符串
 * @returns Map<number, string> 解析后的键值对映射，key为数字，value为字符串
 * @throws Error 当输入格式不正确时抛出异常
 */
export function parseCode(codeString: string): Map<number, string> {
  // 创建结果 Map 对象
  const resultMap = new Map<number, string>()

  // 输入验证：检查是否为空字符串或null/undefined
  if (!codeString || typeof codeString !== 'string') {
    throw new Error('输入的代码字符串不能为空且必须是字符串类型')
  }

  // 去除首尾空格
  const trimmedCode = codeString.trim()

  // 如果字符串为空，返回空的 Map
  if (trimmedCode.length === 0) {
    return resultMap
  }

  try {
    // 使用正则表达式匹配模式：数字#内容$
    // 正则说明：(\d+)# 匹配数字和#，([^#$]+) 匹配非#和$的内容，\$? 匹配可选的$
    const regex = /(\d+)#([^#$]+)\$?/g
    let match: RegExpExecArray | null

    // 循环匹配所有符合规则的片段
    while ((match = regex.exec(trimmedCode)) !== null) {
      const key = parseInt(match[1], 10) // 将匹配到的数字字符串转换为数字
      const value = match[2] // 获取#和$之间的内容

      // 验证 key 是否为有效数字
      if (isNaN(key)) {
        console.warn(`解析到无效的 key: ${match[1]}，跳过此片段`)
        continue
      }

      // 验证 value 是否为空
      if (!value || value.trim().length === 0) {
        console.warn(`key ${key} 对应的 value 为空，跳过此片段`)
        continue
      }

      // 检查是否存在重复的 key
      if (resultMap.has(key)) {
        console.warn(`发现重复的 key: ${key}，新值 "${value}" 将覆盖旧值 "${resultMap.get(key)}"`)
      }

      // 将解析结果添加到 Map 中
      resultMap.set(key, value.trim())
    }

    // 如果没有匹配到任何有效片段，给出提示
    if (resultMap.size === 0) {
      console.warn('未能解析到任何有效的键值对，请检查输入格式是否正确')
    }

    return resultMap
  } catch (error) {
    // 捕获解析过程中的异常
    throw new Error(`代码解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 将解析结果转换为普通对象
 *
 * @param codeString 待解析的代码字符串
 * @returns Record<number, string> 解析后的键值对对象
 */
export function parseCodeToObject(codeString: string): Record<number, string> {
  const resultMap = parseCode(codeString)
  const resultObject: Record<number, string> = {}

  // 将 Map 转换为普通对象
  resultMap.forEach((value, key) => {
    resultObject[key] = value
  })

  return resultObject
}

/**
 * 格式化显示解析结果
 *
 * @param codeString 待解析的代码字符串
 * @returns string 格式化后的结果字符串
 */
export function formatParseResult(codeString: string): string {
  try {
    const resultMap = parseCode(codeString)

    if (resultMap.size === 0) {
      return '解析结果为空'
    }

    const lines: string[] = []
    lines.push(`解析结果 (共 ${resultMap.size} 项):`)

    // 按 key 排序显示
    const sortedEntries = Array.from(resultMap.entries()).sort((a, b) => a[0] - b[0])

    sortedEntries.forEach(([key, value]) => {
      lines.push(`  ${key}: ${value}`)
    })

    return lines.join('\n')
  } catch (error) {
    return `解析失败: ${error instanceof Error ? error.message : '未知错误'}`
  }
}

/**
 * 验证代码字符串格式是否正确
 *
 * 严格校验规则：
 * 1. 必须以数字#开头
 * 2. 必须以$结尾
 * 3. 中间部分必须符合 数字#内容$ 的格式
 *
 * 例如：
 * - "10#Rssdsfs$17#ssaasss$" ✓ 通过
 * - "10#Rssdsfs$17#ssaasss$ssdf" ✗ 不通过（末尾有多余内容）
 * - "abc10#Rssdsfs$" ✗ 不通过（开头不是数字#）
 *
 * @param codeString 待验证的代码字符串
 * @returns boolean 格式是否正确
 */
export function validateCodeFormat(codeString: string): boolean {
  try {
    console.log(codeString, 'codeString')
    // 输入验证：检查是否为空字符串或null/undefined
    if (!codeString || typeof codeString !== 'string') {
      return false
    }
    console.log(codeString, 'codeString1')

    // 去除首尾空格
    const trimmedCode = codeString.trim()

    // 如果字符串为空，返回false
    if (trimmedCode.length === 0) {
      return false
    }
    console.log(codeString, 'codeString2')

    // 严格格式校验：必须以数字#开头，以$结尾
    const strictFormatRegex = /^\d+#.*\$$/
    if (!strictFormatRegex.test(trimmedCode)) {
      return false
    }
    console.log(codeString, 'codeString3')

    // 进一步校验：整个字符串必须完全由 数字#内容$ 的模式组成，不能有多余内容
    const fullMatchRegex = /^(\d+#[^#$]+\$)+$/
    if (!fullMatchRegex.test(trimmedCode)) {
      return false
    }
    console.log(codeString, 'codeString4')

    // 最后通过解析验证是否能正确解析出内容
    const resultMap = parseCode(codeString)
    console.log(resultMap, 'resultMap')

    return resultMap.size > 0
  } catch {
    return false
  }
}
