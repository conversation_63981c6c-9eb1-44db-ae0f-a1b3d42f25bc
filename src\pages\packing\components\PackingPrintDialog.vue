<!-- 装箱打印弹框组件 -->
<template>
  <wd-popup
    v-model="visible"
    position="center"
    :close-on-click-modal="false"
    :custom-style="popupStyle"
  >
    <view class="print-dialog" :class="{ 'print-dialog--pad': isPad }">
      <!-- 弹框标题 -->
      <view class="dialog-header">
        <view class="dialog-title">
          <wd-icon name="check-circle" size="24px" color="#52c41a" class="success-icon"></wd-icon>
          装箱成功
        </view>
        <view class="dialog-subtitle">请打印装箱标签</view>
      </view>

      <!-- 打印内容区域 -->
      <view class="print-content">
        <view v-html="previewTemplate"></view>
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <wd-button size="large" :custom-style="closeButtonStyle" @click="handleClose">
          关闭
        </wd-button>
        <wd-button
          type="primary"
          size="large"
          :loading="printing"
          :custom-style="printButtonStyle"
          @click="handlePrint"
        >
          <wd-icon name="printer" size="16px" class="button-icon"></wd-icon>
          {{ printing ? '打印中...' : '打印标签' }}
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { IResponsePackingInfo } from '@/service/packing/index'
import { printApi } from '@/service/print/index'
import { useToast } from 'wot-design-uni'
import dayjs from 'dayjs'
import { watch, ref } from 'vue'

interface Props {
  modelValue: boolean
  printInfo: IResponsePackingInfo
  productCount?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  productCount: 0,
})

watch(props, (newValue) => {
  console.log(newValue, 'props')
})

const emit = defineEmits<Emits>()

// 初始化 toast 组件
const toast = useToast()

// 内部打印状态管理
const printing = ref(false)

// 计算属性：弹框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
})

// 检测是否为 pad 端
const isPad = computed(() => {
  const { screenWidth } = uni.getSystemInfoSync()
  return screenWidth >= 768
})

// 弹框样式
const popupStyle = computed(() => {
  if (isPad.value) {
    return {
      width: '560px',
      maxHeight: '80vh',
      borderRadius: '16px',
    }
  }
  return {
    width: '90vw',
    maxWidth: '380px',
    maxHeight: '85vh',
    borderRadius: '12px',
  }
})

// 按钮样式
const closeButtonStyle = computed(() => {
  return {
    flex: '1',
    marginRight: isPad.value ? '16px' : '12px',
  }
})

const printButtonStyle = computed(() => {
  return {
    flex: '1.5',
  }
})

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 打印预览模板
const previewTemplate = computed(
  () => `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>供应商零件信息表格</title>
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 2%;
            background-color: #f5f5f5;
        }
        
        .table-container {
            background-color: white;
            padding: 3%;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 100%;
            width: 100%;
            margin: 0 auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            table-layout: fixed;
        }
        
        td {
            border: 2px solid #333;
            text-align: center;
            vertical-align: middle;
            font-size: clamp(12px, 2vw, 16px);
            word-wrap: break-word;
        }
        
        .label-cell {
            background-color: #fff;
            font-weight: bold;
            color: #333;
            width: 35%;
        }
        
        .value-cell {
            background-color: white;
            color: #333;
        }
        
        .value-cell-wide {
            background-color: white;
            color: #333;
            width: 50%;
        }
        
        .qr-cell {
            width: 25%;
            padding: 1%;
        }
        
        .qr-code {
            width: 100%;
            aspect-ratio: 1;
            max-width: 100px;
            background-color: white;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: clamp(8px, 1.5vw, 12px);
            color: #666;
            flex-direction: column;
            line-height: 1.2;
        }
        
        .multi-row {
            height: 60px;
        }
        
        /* 平板设备 */
        @media (max-width: 768px) {
            body {
                padding: 1%;
            }
            
            .table-container {
                padding: 2%;
            }
            
            td {
                padding: 1.5%;
            }
            
            .label-cell {
                width: 30%;
            }
            
            .value-cell-wide {
                width: 45%;
            }
            
            .qr-cell {
                width: 25%;
            }
        }
        
        /* 手机设备 */
        @media (max-width: 480px) {
            body {
                padding: 1%;
            }
            
            .table-container {
                padding: 1%;
            }
            
            td {
                padding: 1%;
                font-size: clamp(10px, 3vw, 14px);
            }
            
            .label-cell {
                width: 35%;
            }
            
            .value-cell-wide {
                width: 40%;
            }
            
            .qr-cell {
                width: 25%;
            }
            
            .qr-code {
                max-width: 80px;
                font-size: clamp(6px, 2vw, 10px);
            }
        }
        
        /* 超小屏幕 */
        @media (max-width: 320px) {
            .label-cell {
                width: 40%;
                font-size: clamp(8px, 3.5vw, 12px);
            }
            
            .value-cell-wide {
                width: 35%;
            }
            
            .qr-cell {
                width: 25%;
            }
            
            .qr-code {
                max-width: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="table-container">
        <table>
            <tr>
                <td class="label-cell">供应商名称/代码</td>
                <td class="value-cell" colspan="2">${props.printInfo.supplierName}/${props.printInfo.supplierCode}</td>
            </tr>
            <tr>
                <td class="label-cell">客户零件号</td>
                <td class="value-cell" colspan="2">${props.printInfo.partCode}</td>
            </tr>
            <tr>
                <td class="label-cell">零件名称</td>
                <td class="value-cell" colspan="2">${props.printInfo.partName}</td>
            </tr>
            <tr>
                <td class="label-cell">单包数量（QTY）</td>
                <td class="value-cell" colspan="2">${props.printInfo.singleQuantity}</td>
            </tr>
            <tr>
                <td class="label-cell">供货批次号（LOT NO）</td>
                <td class="value-cell" colspan="2">${props.printInfo.lotNo}</td>
            </tr>
            <tr>
                <td class="label-cell">码放层数</td>
                <td class="value-cell-wide">4</td>
                <td class="qr-cell" rowspan="4">
                    <div class="qr-code">
                        <img src='${props.printInfo.qrcode}' style="width:100%;height:100%;max-width:120px;max-height:120px;object-fit:contain;" />
                    </div>
                </td>
            </tr>
            <tr>
                <td class="label-cell">生产日期</td>
                <td class="value-cell-wide">${props.printInfo.produceDate}</td>
            </tr>
            <tr class="multi-row">
                <td class="label-cell">检验确认/日期</td>
                <td class="value-cell-wide"></td>
            </tr>
            <tr>
                <td class="label-cell">流水号</td>
                <td class="value-cell-wide">${props.printInfo.serialNo}</td>
            </tr>
        </table>
    </div>
</body>
</html>`,
)

// 打印模板
const printTemplate = computed(
  () => `<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>供应商零件信息表格</title>
    <style >
        body {
            font-family: "SimSun", "宋体", serif;
            margin: 20px;
            background-color: #f5f5f5;
          	width: 1100px;
        }
        
        .table-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 0 auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            background-color: white;
        }
        
        td {
            border: 2px solid #333;
            padding: 12px 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 26px;
            font-weight: bold;
        }
        
        .label-cell {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            width: 35%;
        }
        
        .value-cell {
            background-color: white;
            font-weight: bold;
            color: #333;
        }
        
        .qr-cell {
            width: 330px;
        }
        
        .qr-code {
            width: 330px;
            height: 150px;
            background-color: white;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 12px;
            color: #666;
        }
        
        .multi-row {
            height: 60px;
        }
        
        @media (max-width: 600px) {
            .table-container {
                padding: 10px;
            }
            
            td {
                padding: 8px 10px;
                font-size: 14px;
            }
            
            .qr-code {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="table-container">
        <table>
            <tr>
                <td class="label-cell">供应商名称/代码</td>
                <td class="value-cell" colspan="2">${props.printInfo.supplierName}/${props.printInfo.supplierCode}</td>
            </tr>
            <tr>
                <td class="label-cell">客户零件号</td>
                <td class="value-cell" colspan="2">${props.printInfo.partCode}</td>
            </tr>
            <tr>
                <td class="label-cell">零件名称</td>
                <td class="value-cell" colspan="2">${props.printInfo.partName}</td>
            </tr>
            <tr>
                <td class="label-cell">单包数量（QTY）</td>
                <td class="value-cell" colspan="2">${props.printInfo.singleQuantity}</td>
            </tr>
            <tr>
                <td class="label-cell">供货批次号（LOT NO）</td>
                <td class="value-cell" colspan="2">${props.printInfo.lotNo}</td>
            </tr>
            <tr>
                <td class="label-cell">码放层数</td>
                <td class="value-cell">${props.printInfo.levelNum}</td>
                <td class="qr-cell" rowspan="4">
                    <div class="qr-code">
                        <img src='${props.printInfo.qrcode}' style="width:320px;height:148px" />
                    </div>
                </td>
            </tr>
            <tr>
                <td class="label-cell">生产日期</td>
                <td class="value-cell">${props.printInfo.produceDate}</td>
            </tr>
            <tr class="multi-row">
                <td class="label-cell">检验确认/日期</td>
                <td class="value-cell"></td>
            </tr>
            <tr>
                <td class="label-cell">流水号</td>
                <td class="value-cell">${props.printInfo.serialNo}</td>
            </tr>
        </table>
    </div>
</body>
</html>`,
)

// 事件处理
const handleClose = () => {
  emit('close')
}

// 打印处理函数
const handlePrint = async () => {
  try {
    // 设置打印状态为加载中
    printing.value = true
    toast.show('正在打印...')

    // 调用打印API，传入指定的参数
    const response = await printApi({
      widthPixel: 1100, // 宽度像素设置为1100
      heightPixel: 1300, // 高度像素设置为1300
      content: printTemplate.value.replace('scoped', ''), // 内容为整个HTML模板
    })
    console.log(printTemplate.value.replace('scoped', ''), '模板')

    // 检查打印结果
    if (response.data) {
      toast.show('打印成功')
      // 打印成功后关闭弹框
      setTimeout(() => {
        emit('close')
      }, 1000)
    } else {
      toast.error(response.msg || '打印失败')
    }
  } catch (error) {
    console.error('打印失败:', error)
    toast.error('打印失败，请重试')
  } finally {
    // 无论成功失败都要取消打印状态
    printing.value = false
  }
}
</script>

<style scoped>
.print-dialog {
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  overflow: hidden;
  background: #ffffff;
  border-radius: 12px;
}

.print-dialog--pad {
  border-radius: 16px;
}

.dialog-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-title {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.success-icon {
  flex-shrink: 0;
}

.dialog-subtitle {
  font-size: 14px;
  color: #666666;
}

.print-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  max-height: none; /* 移除最大高度限制，让内容完整展示 */
}

/* 确保 v-html 渲染的内容能够完整显示 */
.print-content :deep(.table-container) {
  max-width: none !important;
  width: 100% !important;
}

.print-content :deep(table) {
  width: 100% !important;
  font-size: 12px !important; /* 稍微缩小字体以适应容器 */
}

.print-content :deep(td) {
  padding: 8px 6px !important; /* 减少内边距以节省空间 */
  font-size: 12px !important;
  word-break: break-all; /* 长文本自动换行 */
}

.print-content :deep(.qr-code) {
  width: 80px !important;
  height: 80px !important;
}

.print-content :deep(.qr-code img) {
  width: 70px !important;
  height: 70px !important;
}

.packing-info {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 20px;
  margin-bottom: 16px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
}

.info-left {
  flex-shrink: 0;
}

.qr-code-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.info-right {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.info-label {
  min-width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: #666666;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
}

.info-value--kd {
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  letter-spacing: 1px;
}

.print-preview {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
}

.preview-icon {
  flex-shrink: 0;
}

.preview-text {
  font-size: 13px;
  color: #1890ff;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
}

.button-icon {
  margin-right: 4px;
}
/* pad 端样式调整 */
.print-dialog--pad .dialog-header {
  padding: 32px 32px 20px;
}

.print-dialog--pad .dialog-title {
  font-size: 20px;
}

.print-dialog--pad .print-content {
  padding: 24px 32px;
}

/* pad 端的表格样式优化 */
.print-dialog--pad .print-content :deep(table) {
  font-size: 14px !important; /* pad端稍大的字体 */
}

.print-dialog--pad .print-content :deep(td) {
  padding: 10px 8px !important; /* pad端稍大的内边距 */
  font-size: 14px !important;
}

.print-dialog--pad .print-content :deep(.qr-code) {
  width: 100px !important;
  height: 100px !important;
}

.print-dialog--pad .print-content :deep(.qr-code img) {
  width: 90px !important;
  height: 90px !important;
}

.print-dialog--pad .packing-info {
  gap: 24px;
  padding: 24px;
}

.print-dialog--pad .qr-code-large {
  width: 140px;
  height: 140px;
}

.print-dialog--pad .info-row {
  gap: 12px;
}

.print-dialog--pad .info-label {
  min-width: 90px;
  font-size: 15px;
}

.print-dialog--pad .info-value {
  font-size: 16px;
}

.print-dialog--pad .info-value--kd {
  font-size: 18px;
}

.print-dialog--pad .dialog-actions {
  gap: 16px;
  padding: 24px 32px;
}
/* 滚动条样式 */
.print-content::-webkit-scrollbar {
  width: 6px;
}

.print-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.print-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.print-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
