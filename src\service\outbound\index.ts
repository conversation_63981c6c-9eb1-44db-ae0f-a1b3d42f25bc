import { http } from '@/utils/http'

/**
 *
 * @returns 0 成功 1 已存在出库记录 2 KD码不存在
 */
export function checkKdCodeApi(kdCode: string) {
  return http.post<number>('/api/v1/pass/checkCode', { kdCode })
}

interface ISavePassRecordRequest {
  kdCode: string
  consumerCode: string
}
/**
 * 保存出库记录
 * @param data: ISavePassRecordRequest
 * @returns
 */
export function savePassRecordApi(data: ISavePassRecordRequest) {
  return http.post<boolean>('/api/v1/pass/save', data)
}
