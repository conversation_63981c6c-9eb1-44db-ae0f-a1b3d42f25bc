<route lang="json5" type="page">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '我的',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="my-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <!-- 头像区域 -->
      <view class="avatar-container">
        <view class="avatar">
          {{ userNameFirstChar }}
        </view>
      </view>

      <!-- 用户基本信息 -->
      <view class="user-info">
        <view class="user-name">{{ userInfo?.userName || '未登录' }}</view>
        <view class="user-role">{{ userRoleName }}</view>
      </view>
    </view>

    <!-- 用户详细信息列表 -->
    <view class="info-list">
      <view class="info-item">
        <view class="info-label">登录账号</view>
        <view class="info-value">{{ userInfo?.loginName || '-' }}</view>
      </view>
      <view class="info-item">
        <view class="info-label">手机号码</view>
        <view class="info-value">{{ userInfo?.phonenumber || '-' }}</view>
      </view>

      <view class="info-item">
        <view class="info-label">所属部门</view>
        <view class="info-value">{{ userInfo?.dept?.deptName || '-' }}</view>
      </view>
      <view class="info-item">
        <view class="info-label">最后登录</view>
        <view class="info-value">{{ formatLoginDate(userInfo?.loginDate) }}</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <wd-button type="error" size="large" block @click="handleLogout" :loading="logoutLoading">
        退出登录
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import { useToast } from 'wot-design-uni'
import dayjs from 'dayjs'

// 获取用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo as any) // 使用 any 类型来避免类型错误
const toast = useToast()
const logoutLoading = ref(false)

// 获取用户名首字符作为头像
const userNameFirstChar = computed(() => {
  if (userInfo.value?.userName) {
    return userInfo.value.userName.charAt(0)
  }
  return '用'
})

// 获取用户角色名称
const userRoleName = computed(() => {
  if (userInfo.value?.roles && userInfo.value.roles.length > 0) {
    return userInfo.value.roles[0].roleName || '普通用户'
  }
  return '普通用户'
})

// 格式化登录时间
const formatLoginDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

// 退出登录处理
const handleLogout = async () => {
  try {
    logoutLoading.value = true

    // 清除用户信息
    userStore.clearUserInfo()

    toast.success('退出登录成功')

    // 跳转到登录页面
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login',
      })
    }, 1000)
  } catch (error) {
    console.error('退出登录失败:', error)
    toast.error('退出登录失败')
  } finally {
    logoutLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.my-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.avatar-container {
  margin-right: 20px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #1890ff;
  color: #ffffff;
  font-size: 36px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6px;
}

.user-role {
  font-size: 14px;
  color: #666666;
}

.info-list {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 90px;
  font-size: 15px;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 15px;
  color: #333333;
}

.action-section {
  margin-top: 20px;
  padding: 0 4px;
}
</style>
