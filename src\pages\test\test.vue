<route lang="json5" type="page">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="pt-40 text-xl text-center text-green-500">我的页面</view>
  <wd-button @click="printTest">打印测试</wd-button>
</template>

<script lang="ts" setup>
import { printApi } from '@/service/print'

//
function printTest() {
  const res = printApi({
    widthPixel: 1100,
    heightPixel: 1300,
    content: `<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>供应商零件信息表格</title>
    <style >
        body {
            font-family: "SimSun", "宋体", serif;
            margin: 20px;
            background-color: #f5f5f5;
          	width: 1100px;
        }
        
        .table-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 0 auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
            background-color: white;
        }
        
        td {
            border: 2px solid #333;
            padding: 12px 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 26px;
            font-weight: bold;
        }
        
        .label-cell {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            width: 35%;
        }
        
        .value-cell {
            background-color: white;
            font-weight: bold;
            color: #333;
        }
        
        .qr-cell {
            width: 330px;
        }
        
        .qr-code {
            width: 330px;
            height: 150px;
            background-color: white;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 12px;
            color: #666;
        }
        
        .multi-row {
            height: 60px;
        }
        
        @media (max-width: 600px) {
            .table-container {
                padding: 10px;
            }
            
            td {
                padding: 8px 10px;
                font-size: 14px;
            }
            
            .qr-code {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="table-container">
        <table>
            <tr>
                <td class="label-cell">供应商名称/代码</td>
                <td class="value-cell" colspan="2">昆山摩恒工业科技有限公司/8SU</td>
            </tr>
            <tr>
                <td class="label-cell">客户零件号</td>
                <td class="value-cell" colspan="2">403006635AA</td>
            </tr>
            <tr>
                <td class="label-cell">零件名称</td>
                <td class="value-cell" colspan="2">遮阳帘ZEEKER</td>
            </tr>
            <tr>
                <td class="label-cell">单包数量（QTY）</td>
                <td class="value-cell" colspan="2">6</td>
            </tr>
            <tr>
                <td class="label-cell">供货批次号（LOT NO）</td>
                <td class="value-cell" colspan="2">27873-CT1820250721</td>
            </tr>
            <tr>
                <td class="label-cell">码放层数</td>
                <td class="value-cell">4</td>
                <td class="qr-cell" rowspan="4">
                    <div class="qr-code">
                        <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAIAAADTED8xAAAcRklEQVR4Xu3TQY4jSxLE0H//S8/sXxCQwxEpdbfEZYE0DxWQ//3vx48v5j//8OPHN/H7AH58Nb8P4MdX8/sAfnw1vw/gx1fz+wB+fDW/D+DHV/P7AH58Nb8P4MdX8/sAfnw1vw/gx1fz+wB+fDW/D+DHV/P7AH58Nb8P4MdX8/sAfnw1mw/gv4/iawqbqjTuOSc2VWmUcwsv1S2NGa68F18zYNV8FF9T2FSlcc85salKo5xbeKluacxw5b34mgGr5qP4msKmKo17zolNVRrl3MJLdUtjhivvxdcMWDUfxdcUNlVp3HNObKrSKOcWXqpbGjNceS++ZsCq+Si+prCpSuOec2JTlUY5t/BS3dKY4cp78TUDVs1H8TWFTVUa95wTm6o0yrmFl+qWxgxX3ouvGbBqDjTu4aXZLZvCZoYrsx2bqjTKeSe+ZvYem1m1w0urW6vmQOMeXprdsilsZrgy27GpSqOcd+JrZu+xmVU7vLS6tWoONO7hpdktm8JmhiuzHZuqNMp5J75m9h6bWbXDS6tbq+ZA4x5emt2yKWxmuDLbsalKo5x34mtm77GZVTu8tLq1ag407uGl2S2bwmaGK7Mdm6o0ynknvmb2HptZtcNLq1ur5kDjHl6a3bIpbGa4MtuxqUqjnHfia2bvsZlVO7y0urVqDjTKmeBK7WjMnBObGa7cw0tP4u26rjHDldrRmOFK7WgMWDUHGuVMcKV2NGbOic0MV+7hpSfxdl3XmOFK7WjMcKV2NAasmgONcia4UjsaM+fEZoYr9/DSk3i7rmvMcKV2NGa4UjsaA1bNgUY5E1ypHY2Zc2Izw5V7eOlJvF3XNWa4UjsaM1ypHY0Bq+ZAo5wJrtSOxsw5sZnhyj289CTerusaM1ypHY0ZrtSOxoBVc6BRzgRXakdj5pzYzHDlHl56Em/XdY0ZrtSOxgxXakdjwKo50Chngiu1o1HOBFdmuFI7Gvfw0gxXCpt7lUY5E1ypHY0Bq+ZAo5wJrtSORjkTXJnhSu1o3MNLM1wpbO5VGuVMcKV2NAasmgONcia4Ujsa5UxwZYYrtaNxDy/NcKWwuVdplDPBldrRGLBqDjTKmeBK7WiUM8GVGa7UjsY9vDTDlcLmXqVRzgRXakdjwKo50Chngiu1o1HOBFdmuFI7Gvfw0gxXCpt7lUY5E1ypHY0Bq+ZAo5wJrtSORjkTXJnhSu1o3MNLM1wpbO5VGuVMcKV2NAasmgONcia4Ujsa5ZzYzKpbeLuwqUpji7u1rDHDldrRmOFK7WgMWDUHGuVMcKV2NMo5sZlVt/B2YVOVxhZ3a1ljhiu1ozHDldrRGLBqDjTKmeBK7WiUc2Izq27h7cKmKo0t7tayxgxXakdjhiu1ozFg1RxolDPBldrRKOfEZlbdwtuFTVUaW9ytZY0ZrtSOxgxXakdjwKo50Chngiu1o1HOic2suoW3C5uqNLa4W8saM1ypHY0ZrtSOxoBVc6BRzgRXakejnBObWXULbxc2VWlscbeWNWa4UjsaM1ypHY0Bq+ZA4x5eqlsahU1VGoVNVRpb58RmVu3w0uyWzaza4aXVrVVzoHEPL9UtjcKmKo3CpiqNrXNiM6t2eGl2y2ZW7fDS6taqOdC4h5fqlkZhU5VGYVOVxtY5sZlVO7w0u2Uzq3Z4aXVr1Rxo3MNLdUujsKlKo7CpSmPrnNjMqh1emt2ymVU7vLS6tWoONO7hpbqlUdhUpVHYVKWxdU5sZtUOL81u2cyqHV5a3Vo1Bxr38FLd0ihsqtIobKrS2DonNrNqh5dmt2xm1Q4vrW6tmo/ia+o9Gj/nc8478TUDVs1H8TX1Ho2f8znnnfiaAavmo/iaeo/Gz/mc8058zYBV81F8Tb1H4+d8znknvmbAqvkovqbeo/FzPue8E18zYNV8FF9T79H4OZ9z3omvGbBp/jT8N9Q/QuPTzg53Z7gyw5XC5i/kn/gNBxp/nrPD3RmuzHClsPkL+Sd+w4HGn+fscHeGKzNcKWz+Qv6J33Cg8ec5O9yd4coMVwqbv5B/4jccaPx5zg53Z7gyw5XC5i/kn/gNBxp/nrPD3RmuzHClsPkL2fwG/w31j9C4h5e2uLtddmW2YzPDlS3u1rJGYTOrdnjpEptdn1aP07iHl7a4u112ZbZjM8OVLe7WskZhM6t2eOkSm12fVo/TuIeXtri7XXZltmMzw5Ut7tayRmEzq3Z46RKbXZ9Wj9O4h5e2uLtddmW2YzPDlS3u1rJGYTOrdnjpEptdn1aP07iHl7a4u112ZbZjM8OVLe7WskZhM6t2eOkSm12fVo/TuIeXtri7XXZltmMzw5Ut7tayRmEzq3Z46RJP7U7wJxY2hU1hU5VGOTvc3eJuLWsUNlVpPIm333j9fZdO/NGFTWFT2FSlUc4Od7e4W8sahU1VGk/i7Tdef9+lE390YVPYFDZVaZSzw90t7tayRmFTlcaTePuN19936cQfXdgUNoVNVRrl7HB3i7u1rFHYVKXxJN5+4/X3XTrxRxc2hU1hU5VGOTvc3eJuLWsUNlVpPIm333j9fZdO/NGFTWFT2FSlUc4Od7e4W8sahU1VGk/i7Tdev3PJ5xc2M1zZ4m5hM8OVwqawqUrjnnNicw8v1S2NwmZWwaY58SGFzQxXtrhb2MxwpbApbKrSuOec2NzDS3VLo7CZVbBpTnxIYTPDlS3uFjYzXClsCpuqNO45Jzb38FLd0ihsZhVsmhMfUtjMcGWLu4XNDFcKm8KmKo17zonNPbxUtzQKm1kFm+bEhxQ2M1zZ4m5hM8OVwqawqUrjnnNicw8v1S2NwmZWwaY58SGFzQxXtrhb2MxwpbApbKrSuOec2NzDS3VLo7CZVbBpTnzIFnfv4aW6pTHDldrR2OLu37B84qXCprCZVbBpTnzIFnfv4aW6pTHDldrR2OLu37B84qXCprCZVbBpTnzIFnfv4aW6pTHDldrR2OLu37B84qXCprCZVbBpTnzIFnfv4aW6pTHDldrR2OLu37B84qXCprCZVbBpTnzIFnfv4aW6pTHDldrR2OLu37B84qXCprCZVbBpTnzIFnfv4aW6pTHDldrR2OLu37B84qXCprCZVbBqDjS2uLvF3VrWmDkTXJnhSu1obHG3ljXKObGZ4UrtaMycl6yaA40t7m5xt5Y1Zs4EV2a4UjsaW9ytZY1yTmxmuFI7GjPnJavmQGOLu1vcrWWNmTPBlRmu1I7GFndrWaOcE5sZrtSOxsx5yao50Nji7hZ3a1lj5kxwZYYrtaOxxd1a1ijnxGaGK7WjMXNesmoONLa4u8XdWtaYORNcmeFK7WhscbeWNco5sZnhSu1ozJyXrJoDjS3ubnG3ljVmzgRXZrhSOxpb3K1ljXJObGa4UjsaM+clm+bEh8xwZYYrsx2bqjS2zonNDFdqR6OcE5st7m6XXakdjXIWXFpZ4coMV2Y7NlVpbJ0Tmxmu1I5GOSc2W9zdLrtSOxrlLLi0ssKVGa7Mdmyq0tg6JzYzXKkdjXJObLa4u112pXY0yllwaWWFKzNcme3YVKWxdU5sZrhSOxrlnNhscXe77ErtaJSz4NLKCldmuDLbsalKY+uc2MxwpXY0yjmx2eLudtmV2tEoZ8GllRWuzHBltmNTlcbWObGZ4UrtaJRzYrPF3e2yK7WjUc6COysTfH5hU5XGp/F99UKNck5sZtUEd2tZY4u7M1ypHY1yXrJpdvjYwqYqjU/j++qFGuWc2MyqCe7WssYWd2e4Ujsa5bxk0+zwsYVNVRqfxvfVCzXKObGZVRPcrWWNLe7OcKV2NMp5yabZ4WMLm6o0Po3vqxdqlHNiM6smuFvLGlvcneFK7WiU85JNs8PHFjZVaXwa31cv1CjnxGZWTXC3ljW2uDvDldrRKOclm2aHjy1sqtL4NL6vXqhRzonNrJrgbi1rbHF3hiu1o1HOS1bNgUY5JzZVaTzp7HC3sHkSb9d1jcLm05VGYTNg1RxolHNiU5XGk84OdwubJ/F2XdcobD5daRQ2A1bNgUY5JzZVaTzp7HC3sHkSb9d1jcLm05VGYTNg1RxolHNiU5XGk84OdwubJ/F2XdcobD5daRQ2A1bNgUY5JzZVaTzp7HC3sHkSb9d1jcLm05VGYTNg1RxolHNiU5XGk84OdwubJ/F2XdcobD5daRQ2A1bNgUZhU9hUpbF1drg7W7Z5L76msKlKo7CZ4cq9nZesmgONwqawqUpj6+xwd7Zs8158TWFTlUZhM8OVezsvWTUHGoVNYVOVxtbZ4e5s2ea9+JrCpiqNwmaGK/d2XrJqDjQKm8KmKo2ts8Pd2bLNe/E1hU1VGoXNDFfu7bxk1RxoFDaFTVUaW2eHu7Nlm/fiawqbqjQKmxmu3Nt5yao50ChsCpuqNLbODndnyzbvxdcUNlVpFDYzXLm385JVc6BRzg53a1mjnB3uFjZVaRQ2s2qHl+qWRmEzq3Z46dKtzYoPqadobHG3ljXK2eFuYVOVRmEzq3Z4qW5pFDazaoeXLt3arPiQeorGFndrWaOcHe4WNlVpFDazaoeX6pZGYTOrdnjp0q3Nig+pp2hscbeWNcrZ4W5hU5VGYTOrdnipbmkUNrNqh5cu3dqs+JB6isYWd2tZo5wd7hY2VWkUNrNqh5fqlkZhM6t2eOnSrc2KD6mnaGxxt5Y1ytnhbmFTlUZhM6t2eKluaRQ2s2qHly7durMywefPfoDNrDpxZbtz4m5hU9hUpVHY3Ks0ypngSmFTlUY5L9k0O3zs7Lk2s+rEle3OibuFTWFTlUZhc6/SKGeCK4VNVRrlvGTT7PCxs+fazKoTV7Y7J+4WNoVNVRqFzb1Ko5wJrhQ2VWmU85JNs8PHzp5rM6tOXNnunLhb2BQ2VWkUNvcqjXImuFLYVKVRzks2zQ4fO3uuzaw6cWW7c+JuYVPYVKVR2NyrNMqZ4EphU5VGOS/ZNDt87Oy5NrPqxJXtzom7hU1hU5VGYXOv0ihngiuFTVUa5bxk09zC5xc2VWlscXeLuzNcmeFKYVOVRmFzDy/VLY3CZsCmuYXPL2yq0tji7hZ3Z7gyw5XCpiqNwuYeXqpbGoXNgE1zC59f2FSlscXdLe7OcGWGK4VNVRqFzT28VLc0CpsBm+YWPr+wqUpji7tb3J3hygxXCpuqNAqbe3ipbmkUNgM2zS18fmFTlcYWd7e4O8OVGa4UNlVpFDb38FLd0ihsBmyaW/j8wqYqjS3ubnF3hiszXClsqtIobO7hpbqlUdgM2DQTfNrscTaFTWFTlcbM2eFuYVPYFDZVaWydE5sZrtSOxsx5yaaZ4NNmj7MpbAqbqjRmzg53C5vCprCpSmPrnNjMcKV2NGbOSzbNBJ82e5xNYVPYVKUxc3a4W9gUNoVNVRpb58Rmhiu1ozFzXrJpJvi02eNsCpvCpiqNmbPD3cKmsClsqtLYOic2M1ypHY2Z85JNM8GnzR5nU9gUNlVpzJwd7hY2hU1hU5XG1jmxmeFK7WjMnJdsmgk+bfY4m8KmsKlKY+bscLewKWwKm6o0ts6JzQxXakdj5rxk1axwpXY0Cpt7eGl2y2aGK4XNrJrg7pPLt/DSpVubFR8yw5Xa0Shs7uGl2S2bGa4UNrNqgrtPLt/CS5dubVZ8yAxXakejsLmHl2a3bGa4UtjMqgnuPrl8Cy9durVZ8SEzXKkdjcLmHl6a3bKZ4UphM6smuPvk8i28dOnWZsWHzHCldjQKm3t4aXbLZoYrhc2smuDuk8u38NKlW5sVHzLDldrRKGzu4aXZLZsZrhQ2s2qCu08u38JLl25tVnxIPUWjsJlVE9ytZY2tc2JT2Hy60riHlwqbJytYNQca5ZzYzKoJ7tayxtY5sSlsPl1p3MNLhc2TFayaA41yTmxm1QR3a1lj65zYFDafrjTu4aXC5skKVs2BRjknNrNqgru1rLF1TmwKm09XGvfwUmHzZAWr5kCjnBObWTXB3VrW2DonNoXNpyuNe3ipsHmyglVzoFHOic2smuBuLWtsnRObwubTlcY9vFTYPFnBpjnxIfUUjXImuFLYVKVR2FSlUc4EV2pHo5wd7hY2hc2T1RXuXPL59QM0ypngSmFTlUZhU5VGORNcqR2Ncna4W9gUNk9WV7hzyefXD9AoZ4IrhU1VGoVNVRrlTHCldjTK2eFuYVPYPFld4c4ln18/QKOcCa4UNlVpFDZVaZQzwZXa0Shnh7uFTWHzZHWFO5d8fv0AjXImuFLYVKVR2FSlUc4EV2pHo5wd7hY2hc2T1RXuXPL59QM0ypngSmFTlUZhU5VGORNcqR2Ncna4W9gUNk9WV3jfpRN/dP1sjZlzYnOv0pjhynZnh7frukZhM6tOXKkdjZnzkk1zC59fP0Bj5pzY3Ks0Zriy3dnh7bquUdjMqhNXakdj5rxk09zC59cP0Jg5Jzb3Ko0Zrmx3dni7rmsUNrPqxJXa0Zg5L9k0t/D59QM0Zs6Jzb1KY4Yr250d3q7rGoXNrDpxpXY0Zs5LNs0tfH79AI2Zc2Jzr9KY4cp2Z4e367pGYTOrTlypHY2Z85JNcwufXz9AY+ac2NyrNGa4st3Z4e26rlHYzKoTV2pHY+a8ZNUMsKlK40m8Xdc1yjmxKWy2uPsk3n4Sb9d1jXIWbFZ8SGFTlcaTeLuua5RzYlPYbHH3Sbz9JN6u6xrlLNis+JDCpiqNJ/F2Xdco58SmsNni7pN4+0m8Xdc1ylmwWfEhhU1VGk/i7bquUc6JTWGzxd0n8faTeLuua5SzYLPiQwqbqjSexNt1XaOcE5vCZou7T+LtJ/F2XdcoZ8FmxYcUNlVpPIm367pGOSc2hc0Wd5/E20/i7bquUc6CzYoPqadozJw/DV9cb9YobAqbwubfrU5sqtIYsGoONLbOn4YvrjdrFDaFTWHz71YnNlVpDFg1Bxpb50/DF9ebNQqbwqaw+XerE5uqNAasmgONrfOn4YvrzRqFTWFT2Py71YlNVRoDVs2Bxtb50/DF9WaNwqawKWz+3erEpiqNAavmQGPr/Gn44nqzRmFT2BQ2/251YlOVxoBVc6BR2MxwpXY0CpttdWJTlcYMV+7hpS3u1rLGPefEZsCqOdAobGa4Ujsahc22OrGpSmOGK/fw0hZ3a1njnnNiM2DVHGgUNjNcqR2NwmZbndhUpTHDlXt4aYu7taxxzzmxGbBqDjQKmxmu1I5GYbOtTmyq0pjhyj28tMXdWta455zYDFg1BxqFzQxXakejsNlWJzZVacxw5R5e2uJuLWvcc05sBqyaA43CZoYrtaNR2GyrE5uqNGa4cg8vbXG3ljXuOSc2A1bNgUY5Jzaz6sSV2tGY4cpsx2ZWnbjy5I7Ge/E19R6NS2x2fVo9TqOwmVUnrtSOxgxXZjs2s+rElSd3NN6Lr6n3aFxis+vT6nEahc2sOnGldjRmuDLbsZlVJ648uaPxXnxNvUfjEptdn1aP0yhsZtWJK7WjMcOV2Y7NrDpx5ckdjffia+o9GpfY7Pq0epxGYTOrTlypHY0Zrsx2bGbViStP7mi8F19T79G4xGbXp9XjNAqbWXXiSu1ozHBltmMzq05ceXJH4734mnqPxiWe2n0n/qsKm221w91a1pg5O9zdLrsyw5XCpiqNFXdWPov/mMJmW+1wt5Y1Zs4Od7fLrsxwpbCpSmPFnZXP4j+msNlWO9ytZY2Zs8Pd7bIrM1wpbKrSWHFn5bP4jylsttUOd2tZY+bscHe77MoMVwqbqjRW3Fn5LP5jCptttcPdWtaYOTvc3S67MsOVwqYqjRV3Vj6L/5jCZlvtcLeWNWbODne3y67McKWwqUpjxWbFh7wXX/Mk3r6HlwqbwuYeXipsqtIo58SmsJlVsGo+iq95Em/fw0uFTWFzDy8VNlVplHNiU9jMKlg1H8XXPIm37+GlwqawuYeXCpuqNMo5sSlsZhWsmo/ia57E2/fwUmFT2NzDS4VNVRrlnNgUNrMKVs1H8TVP4u17eKmwKWzu4aXCpiqNck5sCptZBavmo/iaJ/H2PbxU2BQ29/BSYVOVRjknNoXNrIJVc6BxDy/VLY1yTmzu4aXtLVdqR+NJvL297kphU9jMKlg1Bxr38FLd0ijnxOYeXtrecqV2NJ7E29vrrhQ2hc2sglVzoHEPL9UtjXJObO7hpe0tV2pH40m8vb3uSmFT2MwqWDUHGvfwUt3SKOfE5h5e2t5ypXY0nsTb2+uuFDaFzayCVXOgcQ8v1S2Nck5s7uGl7S1XakfjSby9ve5KYVPYzCpYNQca9/BS3dIo58TmHl7a3nKldjSexNvb664UNoXNrIJVc6BRzgRXakfjnnNiU5VGYfNpfF+9UONJvF3XNWbOS1bNgUY5E1ypHY17zolNVRqFzafxffVCjSfxdl3XmDkvWTUHGuVMcKV2NO45JzZVaRQ2n8b31Qs1nsTbdV1j5rxk1RxolDPBldrRuOec2FSlUdh8Gt9XL9R4Em/XdY2Z85JVc6BRzgRXakfjnnNiU5VGYfNpfF+9UONJvF3XNWbOS1bNgUY5E1ypHY17zolNVRqFzafxffVCjSfxdl3XmDkvWTUHGuVMcKV2ND7tnNgUNoVNYTPDldrRmOHKFncLm1kFq+ZAo5wJrtSOxqedE5vCprApbGa4UjsaM1zZ4m5hM6tg1RxolDPBldrR+LRzYlPYFDaFzQxXakdjhitb3C1sZhWsmgONcia4Ujsan3ZObAqbwqawmeFK7WjMcGWLu4XNrIJVc6BRzgRXakfj086JTWFT2BQ2M1ypHY0Zrmxxt7CZVbBqDjTKmeBK7Wh82jmxKWwKm8Jmhiu1ozHDlS3uFjazClbNgUY5E1ypHY1yTmy21YlNYTPDlcJmhiuf3pngpcJmwKo50Chngiu1o1HOic22OrEpbGa4UtjMcOXTOxO8VNgMWDUHGuVMcKV2NMo5sdlWJzaFzQxXCpsZrnx6Z4KXCpsBq+ZAo5wJrtSORjknNtvqxKawmeFKYTPDlU/vTPBSYTNg1RxolDPBldrRKOfEZlud2BQ2M1wpbGa48umdCV4qbAasmgONcia4Ujsa5ZzYbKsTm8JmhiuFzQxXPr0zwUuFzYBVc6BxDy/Nbtncw0t1S6Ow2eJuLWsUNlVpPIm3C5tZBavmQOMeXprdsrmHl+qWRmGzxd1a1ihsqtJ4Em8XNrMKVs2Bxj28NLtlcw8v1S2NwmaLu7WsUdhUpfEk3i5sZhWsmgONe3hpdsvmHl6qWxqFzRZ3a1mjsKlK40m8XdjMKlg1Bxr38NLsls09vFS3NAqbLe7WskZhU5XGk3i7sJlVsGoONO7hpdktm3t4qW5pFDZb3K1ljcKmKo0n8XZhM6tg1XwUX1Pv0ShsZtWJK1vcfS++prApbLbVDncHrJqP4mvqPRqFzaw6cWWLu+/F1xQ2hc222uHugFXzUXxNvUejsJlVJ65scfe9+JrCprDZVjvcHbBqPoqvqfdoFDaz6sSVLe6+F19T2BQ222qHuwNWzUfxNfUejcJmVp24ssXd9+JrCpvCZlvtcHfAqvkovqbeo1HYzKoTV7a4+158TWFT2GyrHe4O2DQ/fvwz/D6AH1/N7wP48dX8PoAfX83vA/jx1fw+gB9fze8D+PHV/D6AH1/N7wP48dX8PoAfX83vA/jx1fw+gB9fze8D+PHV/D6AH1/N7wP48dX8PoAfX83vA/jx1fwfmB8JYN/8mLgAAAAASUVORK5CYII=' style="width:320px;height:148px" />
                    </div>
                </td>
            </tr>
            <tr>
                <td class="label-cell">生产日期</td>
                <td class="value-cell">20250728</td>
            </tr>
            <tr class="multi-row">
                <td class="label-cell">检验确认/日期</td>
                <td class="value-cell"></td>
            </tr>
            <tr>
                <td class="label-cell">流水号</td>
                <td class="value-cell">007</td>
            </tr>
        </table>
    </div>
</body>
</html>`.replace('scoped', ''),
  })
}
</script>

<style lang="scss" scoped>
//
</style>
