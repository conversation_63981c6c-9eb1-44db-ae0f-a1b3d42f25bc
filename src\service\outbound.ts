/**
 * 出库功能相关的API接口
 */

import type {
  OutboundVerifyRequest,
  OutboundVerifyResponse,
  OutboundConfirmRequest,
  OutboundConfirmResponse,
  ApiResponse,
  KdCodeInfo,
  BoxCardInfo,
} from '@/types/outbound'

// 模拟延迟函数
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// 模拟KD码数据库
const mockKdCodeData: Record<string, KdCodeInfo> = {
  'PRJ-2025-001': {
    projectCode: 'PRJ-2025-001',
    batchCode: 'BATCH-001',
    deliveryAddress: '上海市浦东新区xx科技园区',
  },
  'PRJ-2025-002': {
    projectCode: 'PRJ-2025-002',
    batchCode: 'BATCH-002',
    deliveryAddress: '北京市朝阳区xx产业园',
  },
  'PRJ-2025-003': {
    projectCode: 'PRJ-2025-003',
    batchCode: 'BATCH-003',
    deliveryAddress: '深圳市南山区xx科技城',
  },
}

// 模拟随箱卡数据库
const mockBoxCardData: Record<string, BoxCardInfo> = {
  'PRJ-2025-001': {
    projectCode: 'PRJ-2025-001',
    batchCode: 'BATCH-001',
    deliveryAddress: '上海市浦东新区xx科技园区',
  },
  'PRJ-2025-002': {
    projectCode: 'PRJ-2025-002',
    batchCode: 'BATCH-002',
    deliveryAddress: '北京市朝阳区xx产业园',
  },
  'PRJ-2025-003': {
    projectCode: 'PRJ-2025-003',
    batchCode: 'BATCH-003',
    deliveryAddress: '深圳市南山区xx科技城',
  },
}

/**
 * 根据KD码获取信息
 * @param kdCode KD件码
 */
export const getKdCodeInfo = async (kdCode: string): Promise<ApiResponse<KdCodeInfo | null>> => {
  await delay(300) // 模拟网络延迟

  // 支持多种KD码格式进行匹配
  let projectCode: string | null = null

  // 检查是否包含项目代码
  if (kdCode.includes('PRJ-2025-001') || kdCode.includes('001')) {
    projectCode = 'PRJ-2025-001'
  } else if (kdCode.includes('PRJ-2025-002') || kdCode.includes('002')) {
    projectCode = 'PRJ-2025-002'
  } else if (kdCode.includes('PRJ-2025-003') || kdCode.includes('003')) {
    projectCode = 'PRJ-2025-003'
  }

  const kdCodeInfo = projectCode ? mockKdCodeData[projectCode] : null

  return {
    code: 200,
    message: kdCodeInfo ? '获取成功' : 'KD码不存在',
    success: true,
    data: kdCodeInfo ? { ...kdCodeInfo, kdCode } : null,
  }
}

/**
 * 根据随箱卡码获取信息
 * @param boxCardCode 随箱卡码
 */
export const getBoxCardInfo = async (
  boxCardCode: string,
): Promise<ApiResponse<BoxCardInfo | null>> => {
  await delay(300) // 模拟网络延迟

  // 支持多种随箱卡码格式进行匹配
  let projectCode: string | null = null

  // 检查是否包含项目代码
  if (boxCardCode.includes('PRJ-2025-001') || boxCardCode.includes('001')) {
    projectCode = 'PRJ-2025-001'
  } else if (boxCardCode.includes('PRJ-2025-002') || boxCardCode.includes('002')) {
    projectCode = 'PRJ-2025-002'
  } else if (boxCardCode.includes('PRJ-2025-003') || boxCardCode.includes('003')) {
    projectCode = 'PRJ-2025-003'
  }

  const boxCardInfo = projectCode ? mockBoxCardData[projectCode] : null

  return {
    code: 200,
    message: boxCardInfo ? '获取成功' : '随箱卡码不存在',
    success: true,
    data: boxCardInfo ? { ...boxCardInfo, boxCardCode } : null,
  }
}

/**
 * 出库校验
 * @param request 出库校验请求
 */
export const verifyOutbound = async (
  request: OutboundVerifyRequest,
): Promise<ApiResponse<OutboundVerifyResponse>> => {
  await delay(500) // 模拟网络延迟

  // 获取KD码信息
  const kdCodeResponse = await getKdCodeInfo(request.kdCode)
  const kdCodeInfo = kdCodeResponse.data

  // 获取随箱卡信息
  const boxCardResponse = await getBoxCardInfo(request.boxCardCode)
  const boxCardInfo = boxCardResponse.data

  // 判断是否匹配
  const isMatch =
    kdCodeInfo &&
    boxCardInfo &&
    kdCodeInfo.projectCode === boxCardInfo.projectCode &&
    kdCodeInfo.batchCode === boxCardInfo.batchCode

  const message = isMatch
    ? '对比成功'
    : !kdCodeInfo
      ? 'KD码不存在'
      : !boxCardInfo
        ? '随箱卡码不存在'
        : '信息不匹配'

  return {
    code: 200,
    message,
    success: true,
    data: {
      isMatch: !!isMatch,
      kdCodeInfo: kdCodeInfo || {
        projectCode: '',
        batchCode: '',
        deliveryAddress: '',
        kdCode: request.kdCode,
      },
      boxCardInfo: boxCardInfo || {
        projectCode: '',
        batchCode: '',
        deliveryAddress: '',
        boxCardCode: request.boxCardCode,
      },
      message,
    },
  }
}

/**
 * 确认出库
 * @param request 出库确认请求
 */
export const confirmOutbound = async (
  request: OutboundConfirmRequest,
): Promise<ApiResponse<OutboundConfirmResponse>> => {
  await delay(800) // 模拟网络延迟

  // 生成出库单号
  const timestamp = Date.now().toString().slice(-8)
  const outboundNumber = `OUT${timestamp}`

  return {
    code: 200,
    message: '出库成功',
    success: true,
    data: {
      outboundNumber,
      outboundTime: new Date().toISOString(),
      operator: request.operator || '操作员001',
      success: true,
    },
  }
}

/**
 * 模拟扫码功能
 */
export const simulateScanCode = async (type: 'kd' | 'boxcard'): Promise<string> => {
  await delay(800) // 模拟扫码时间

  // 模拟扫码结果
  const mockKdCodes = ['KD-PRJ-2025-001-001', 'KD-PRJ-2025-002-002', 'KD-PRJ-2025-003-003']

  const mockBoxCardCodes = ['BC-PRJ-2025-001-001', 'BC-PRJ-2025-002-002', 'BC-PRJ-2025-003-003']

  const codes = type === 'kd' ? mockKdCodes : mockBoxCardCodes
  const randomIndex = Math.floor(Math.random() * codes.length)
  return codes[randomIndex]
}
